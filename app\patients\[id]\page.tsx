"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { API_BASE_URL, GET_ALL_VISITS_WITH_PATIENT_ID, PATIENTS } from "@/constants/apiRoutes";
import { Annotation, FMS_LAYOUT, Patient, Visit, XrayImage } from "./types";
import { PatientHeader } from "./components/patient-header";
import { FullMouthSeries } from "./components/full-mouth-series";
import { PreviousVisitsTable } from "./components/previous-visits-table";
import { ComparisonTimeline } from "./components/comparison-timeline";
import TreatmentPlanView from "./components/treatment-plan-view";
// import { AIAnnotator } from "./components/ai-annotator";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store/store"; // Import AppDispatch
import { addPatientVisitId, addPreviousPatientsVisits, clearPreviousVisitLoadedImagesCompleteData, clearPreviousVisitLoadedXraysList, resetPreviousVisitClicked } from "@/store/previousVisits";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";

import { Progress } from "@/components/ui/progress";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  setSlots,
  setAnalyzing,
  setUploading,
  setGlobalAnalyzing,
  setIsCollapsed,
  toggleIsCollapsed,
  setPanoramicImage,
  setLoading,
  setError,
  setAnnotations,
  setIsBulkUploading,
  setSelectedAnnotatorImage, // Import new action
} from "@/store/fullMouthSeriesSlice";



import {
  addPreviousVisitLoadedImagesCompleteData,
  addPreviousVisitLoadedXraysList,
  togglePreviousVisitClicked
} from "@/store/previousVisits";


interface TimelineProps {
  xrays: { id: string; date: string; label: string }[]
  currentXrayId: string
  onSelectXray: (id: string) => void
}

// Mock function to simulate fetching annotations from the backend
// In a real application, this would be an actual API call to retrieve stored annotations.
const mockFetchAnnotationsForImage = async (imageId: number, imageType: string, visitId: string): Promise<Annotation | null> => {
  console.log(`Mock fetching annotations for imageId: ${imageId}, imageType: ${imageType}, visitId: ${visitId}`);
  // Simulate a network delay
  await new Promise(resolve => setTimeout(resolve, 200));

  // For demonstration, return a dummy annotation if imageId is even,
  // simulating that some images have pre-existing annotations.
  if (imageId % 2 === 0) {
    return {
      decay: [
        {
          category_id: 1,
          category_name: "Incipient Decay",
          segmentation: [[100, 100, 150, 100, 150, 150, 100, 150, 100, 100]],
          conf_score: 0.9,
          annotation_source: "AI"
        }
      ],
      numbering: [
        {
          category_id: 18,
          category_name: "Tooth 18",
          segmentation: [[200, 200, 250, 200, 250, 250, 200, 250, 200, 200]],
          conf_score: 0.95,
          annotation_source: "AI"
        }
      ]
    };
  }
  return null;
};


export default function PatientPage() {
  const params = useParams();
  const router = useRouter();
  const dispatch: AppDispatch = useDispatch(); // Type the dispatch hook
  const patientId = params.id as string;
  const [visits, setVisits] = useState<Visit[]>([]);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [selectedVisit, setSelectedVisit] = useState<Visit | undefined>(undefined);
  const [annotatorOpen, setAnnotatorOpen] = useState(false);

  const [selectedImage, setSelectedImage] = useState<{
    slotId: string;
    image: XrayImage;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [annotations, setAnnotations] = useState<Record<string, Annotation>>({});
  const visit = useSelector((state: RootState) => state.previousVisit.previousPatientVisits) ?? [];
  const [activeTab, setActiveTab] = useState<"fms"|"comparison"|"treatment">("fms")
  const [runAnalysis, setRunAnalysis] = useState<() => Promise<void>>(() => async () => {});
  const displayName = useSelector((state: RootState) => state.user.displayName);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
const [analysisProgress, setAnalysisProgress] = useState(0);
const [analysisStatus, setAnalysisStatus] = useState<string | null>(null);
  const [currentid,setcurrentid]=useState<string | null>(null); 
  console.log("currentid",currentid);
  // Upload progress states
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  console.log("isuploading",isUploading);
  const [uploadStatus, setUploadStatus] = useState<string | null>(null);
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});


  useEffect(() => {
    const fetchPatient = async () => {
      setLoading(true);
      setError(null);

      const maxRetries = 3;
      let attempts = 0;

      while (attempts < maxRetries) {
        try {
          const res = await fetchWithRefresh(
            `${PATIENTS}/${patientId!}`, // Non-null assertion
            { method: "GET", credentials: "include" },
            router
          );

          if (!res) {
            // Token refresh failed, and router.push('/login') was called in fetchWithRefresh
            return;
          }

          if (!res.ok) {
            if (res.status === 401 && attempts < maxRetries - 1) {
              console.warn(`Attempt ${attempts + 1} failed with 401, retrying...`);
              attempts++;
              await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait 1s before retry
              continue;
            }
            throw new Error("Patient not found");
          }

          const json = await res.json();
          const patientData = json.data;

          setPatient({
            ...patientData,
            name: `${patientData.firstname} ${patientData.lastname}`,
            id: patientData.id.toString(),
          });
          setLoading(false);
          return; // Success, exit the loop
        } catch (err: any) {
          console.error(`Fetch patient attempt ${attempts + 1} failed:`, err);
          attempts++;
          if (attempts >= maxRetries) {
            setError(err.message || "Failed to fetch patient after retries");
            setLoading(false);
          }
        }
      }
    };

    if (patientId) {
      fetchPatient();
    }
  }, [patientId, router]);

  useEffect(() => {
    if (!patientId) return;

    const loadVisits = async () => {
      try {
        const res = await fetchWithRefresh(
          `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`,
          { method: "GET", credentials: "include" },
          router
        );

        if (!res) {
          throw new Error("Session expired, please log in again.");
        }

        if (!res.ok) {
          throw new Error("Failed to fetch visits");
        }

        const body = await res.json();
        const visitsRaw: unknown = Array.isArray(body.data)
          ? body.data
          : Array.isArray(body)
            ? body
            : [];

        const visitsArray = visitsRaw as Visit[];
        const sorted = visitsArray.sort(
          (a, b) => Date.parse(b.createdAt) - Date.parse(a.createdAt)
        );

        setVisits(sorted);

        if (sorted.length > 0) {
          setSelectedVisit(sorted[0]);
        } else {
          setSelectedVisit(undefined);
          dispatch(clearPreviousVisitLoadedXraysList());
          dispatch(clearPreviousVisitLoadedImagesCompleteData());
          dispatch(setSlots({}));
          dispatch(addPatientVisitId(0));
          dispatch(resetPreviousVisitClicked());
        }
      } catch (err: any) {
        console.error("Error loading visits:", err);
      }
    };

    loadVisits();
  }, [patientId, router, dispatch]);

  
const handleLoadImages = async (visit: Visit) => {
  console.log("calling");
    setLoadingImages(prev => ({ ...prev, [visit.visitId]: true }));
    setError(null);

    try {
      const formattedVisitDate = new Date(visit.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
      });
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/api/ImageUploader/image-links/${patientId!}/${visit.visitId}/visitDate?visitDate=${encodeURIComponent(formattedVisitDate)}`, // Non-null assertion
        { method: "GET", credentials: "include" },
        router
      );
  
      // Guard against null (failed refresh → already redirected)
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to fetch images");
      }
  
      const data = await response.json();
      console.log("Loaded Data from previous Visits (images only):", data);
      dispatch(addPreviousVisitLoadedImagesCompleteData(data));
  
      // Transform to XrayImage[]
      const xrays: XrayImage[] = data.results.map((result: any) => ({
        id: result.url.split("/").pop()?.split(".")[0] || `${Date.now()}`,
        imageId: result.id, // imageId is now required
        url: result.url,
        type: result.type,
        date: new Date().toISOString(),
        analyzed: result.analyzed || false,
        findings: [],
      }));
  
      dispatch(addPatientVisitId(parseInt(visit.visitId, 10)));
      dispatch(addPreviousVisitLoadedXraysList(xrays));
      dispatch(togglePreviousVisitClicked());
  
      console.log("Loaded Xrays (metadata):", xrays);
  
      // Fetch annotations for each loaded image
      const allFetchedAnnotations: Record<string, Annotation> = {};
      for (const xray of xrays) {
        if (xray.imageId) {
          // In a real scenario, you'd call your actual backend API here
          // const annotationsForImage = await fetchAnnotationsForImage(xray.imageId, xray.type, visit.visitId);
          const annotationsForImage = await mockFetchAnnotationsForImage(xray.imageId, xray.type, visit.visitId); // Using mock for demonstration

          if (annotationsForImage) {
            allFetchedAnnotations[xray.type.toUpperCase()] = annotationsForImage;
          }
        }
      }
      dispatch(setAnnotations(allFetchedAnnotations)); // Update Redux store with all fetched annotations

      const updatedVisit = {
        ...visit,
        xrays,
        imageCount: data.total,
      };
      handleVisitSelect(updatedVisit);
      // Trigger analysis after images are loaded for the selected visit
      if (runAnalysis) {
        setAnalysisStatus("Starting analysis for loaded visit...");
        setIsAnalyzing(true);
        setAnalysisProgress(0);
        runAnalysis().finally(() => {
          setIsAnalyzing(false);
          setAnalysisStatus(null);
          setAnalysisProgress(0);
        });
      }
    } catch (err: any) {
      console.error("Error loading images or annotations:", err);
      setError(err.message || "Failed to load images or annotations");
    } finally {
      setLoadingImages(prev => ({ ...prev, [visit.visitId]: false }));
    }
  };




  const authState = useSelector((state: RootState) => state.auth);
  const userState = useSelector((state: RootState) => state?.user);
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const onBulkUpload = async (files: FileList | null, router: any) => {
  console.log("Bulk Upload Triggered in page.tsx");

  if (!files || files.length === 0 || !patientId) {
    console.log("No files or patientId provided, aborting upload");
    return null;
  }

  try {
    setIsUploading(true);
    setUploadStatus("Preparing upload...");
    setUploadProgress(5);
 let stopProgress = false;

    // Start smooth progress loop before API triggers
    const startAutoProgress = async () => {
      let progress = 5;
      while (progress < 75 && !stopProgress) {
        setUploadProgress(progress);
        await sleep(1000); // smoother update every 100ms
        progress++;
      }
    };

    startAutoProgress(); // fire progress loop
    const todayString = new Date().toISOString().split("T")[0];

    setUploadStatus("Checking existing visits...");
    // setUploadProgress(10);
    const visitsResponse = await fetchWithRefresh(
      `${GET_ALL_VISITS_WITH_PATIENT_ID}${patientId}`,
      { method: "GET", credentials: "include" },
      router
    );

    if (!visitsResponse || !visitsResponse.ok) {
      console.error("Failed to fetch visits");
      throw new Error("Failed to fetch visits");
    }

    const visits = await visitsResponse.json();
    console.log("Visits response:", visits);

    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    let latestVisit = null;
    if (visits.length > 0) {
      latestVisit = visits.reduce((latest: any, current: any) => {
        const currentDate = new Date(current.visitDate);
        const latestDate = new Date(latest.visitDate);
        return currentDate > latestDate ? current : latest;
      });
    }

    const isSameDate = (d1: Date, d2: Date) =>
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate();

    const useExistingVisit = latestVisit
      ? isSameDate(new Date(latestVisit.visitDate), currentDate)
      : false;

    let visitId = useExistingVisit && latestVisit ? latestVisit.visitId : null;

    if (latestVisit) {
      dispatch(clearPreviousVisitLoadedImagesCompleteData());
      dispatch(clearPreviousVisitLoadedXraysList());
      dispatch(resetPreviousVisitClicked());
    }

    const apiUrl = visitId
      ? `${API_BASE_URL}/api/ImageUploader/uploadImagesToVisit`
      : `${API_BASE_URL}/api/ImageUploader/createVisitAndUploadImages`;

    const formData = new FormData();
    const emptySlots = FMS_LAYOUT.filter((slot) =>
      selectedVisit?.xrays
        ? !selectedVisit.xrays.some((x) => x?.id === slot.id)
        : true
    );

    setUploadStatus("Preparing files...");
    // setUploadProgress(20);
    Array.from(files).forEach((file, idx) => {
      const imageType = emptySlots?.[idx]?.id || "";
      formData.append("images", file);
    });

    formData.append("patientId", patientId);
    if (userState.userId) {
      formData.append("createdBy", String(userState.userId));
    }
    if (visitId) {
      formData.append("patientVisitId", visitId);
    }

    setUploadStatus("Uploading images to server...");
    // setUploadProgress(30);

    const xhr = new XMLHttpRequest();
    xhr.upload.addEventListener("progress", (event) => {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100);
        // setUploadProgress(30 + percentComplete * 0.5); // max 80%
      }
    });

    const response = await new Promise<any>((resolve, reject) => {
      xhr.open("POST", apiUrl, true);
      xhr.withCredentials = true;

      const token = localStorage.getItem("accessToken");
      if (token) {
        xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      }

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            resolve(JSON.parse(xhr.responseText));
          } catch {
            resolve(xhr.responseText);
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error("Upload failed due to network error"));
      xhr.send(formData);
    });

    if (response?.visitId) {
      visitId = response.visitId;
      dispatch(addPatientVisitId(parseInt(visitId, 10)));
    }

    if (!visitId) {
      throw new Error("No visit ID available for loading images");
    }
setUploadStatus("Processing uploaded images...");

  stopProgress = true;

    // Continue with meaningful progress
    setUploadStatus("Analyzing image metadata...");
    setAnalysisStatus("Analyzing...");
    setUploadProgress((prev) => Math.max(prev, 75));
    await sleep(400);

    setUploadStatus("Matching images to slots...");
    setAnalysisStatus("Matching...");
    setUploadProgress(80);
    await sleep(400);

    setUploadStatus("Verifying image formats...");
    setAnalysisStatus("Verifying...");
    setUploadProgress(85);
    await sleep(400);

    setUploadStatus("Finalizing...");
    setAnalysisStatus("Finalizing...");
    setUploadProgress(90);
    await sleep(300);
    const counts = response.uploads.reduce(
      (acc: any, { imageType }: any) => {
        if (imageType === "PANORAMIC") acc.panoramic++;
        else if (imageType.startsWith("MISL")) acc.miscellaneous++;
        else acc.intraoral++;
        return acc;
      },
      { panoramic: 0, intraoral: 0, miscellaneous: 0 }
    );

    const messageParts = [
      counts.panoramic && `${counts.panoramic} Panoramic`,
      counts.intraoral && `${counts.intraoral} Intraoral`,
      counts.miscellaneous && `${counts.miscellaneous} Miscellaneous`
    ]
      .filter(Boolean)
      .join(", ");

    toast.success(`Upload Successful!`);

    if (response?.uploads) {
      const newSlots: Record<string, XrayImage> = {};
      response.uploads.forEach((xray: any) => {
        const slot = FMS_LAYOUT.find((s) => s.id === xray.imageType);
        newSlots[xray.imageType] = slot ? xray : xray;
      });

      dispatch(setSlots(newSlots));
      await fetchVisits();
    }

    const latestVisitByReduce = visits.length > 0 ? visits.reduce((latest: any, current: any) =>
      new Date(current.visitDate) > new Date(latest.visitDate) ? current : latest
    ) : null;

    let visitDateForApi = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "numeric",
      day: "numeric"
    });

    if (useExistingVisit && latestVisit) {
      visitDateForApi = new Date(latestVisit.visitDate).toLocaleDateString("en-US", {
        year: "numeric",
        month: "numeric",
        day: "numeric"
      });
    }

    setUploadStatus("Fetching processed images...");
    setUploadProgress(100);

    const res = await fetchWithRefresh(
      `${API_BASE_URL}/api/ImageUploader/image-links/${patientId}/${visitId}/visitDate?visitDate=${encodeURIComponent(visitDateForApi)}`,
      { method: "GET", credentials: "include" },
      router
    );

    if (!res || !res.ok) {
      throw new Error("Failed to fetch image links");
    }

    const data = await res.json();
    dispatch(addPreviousVisitLoadedImagesCompleteData(data));

    const xrays = data.results.map((result: any) => ({
      id: result.url.split("/").pop()?.split(".")[0] || `${Date.now()}`,
      imageId: result.id,
      url: result.url,
      type: result.type,
      date: new Date().toISOString(),
      analyzed: false,
      findings: []
    }));

    dispatch(addPreviousVisitLoadedXraysList(xrays));
    dispatch(togglePreviousVisitClicked());

    const updatedVisit = {
      ...latestVisitByReduce,
      xrays,
      imageCount: data.total
    };

    handleVisitSelect(updatedVisit);
    return response;
  } catch (error: any) {
    console.error("Error during bulk upload:", error);
    // setUploadStatus(`Error: ${error.message}`);
    toast.error(`Upload failed`);
    throw error;
  } finally {
    setTimeout(() => {
      setIsUploading(false);
      setUploadStatus(null);
      setUploadProgress(0);
    }, 1500);
  }
};

  const fetchVisits = async () => {
    try {
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId!}`, // Non-null assertion
        { method: "GET", credentials: "include" },
        router
      );
 
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to fetch visits");
      }
 
      const data = await response.json();
      const formattedVisits = (data.data ?? data).map((visit: any) => ({
        visitId: visit.visitId.toString(),
        visitDate: new Date(visit.visitDate).toLocaleDateString(),
        dentistName: displayName || visit.dentistName,
        createdAt: new Date(visit.createdAt).toLocaleString(),
        procedures: visit.procedures || [],
        imageCount: visit.imageCount || 0,
      }));
 
      dispatch(addPreviousPatientsVisits(formattedVisits));
    } catch (err: any) {
      console.error("Error fetching visits:", err);
    }
  };
 
 
  // const fetchVisits = async () => {
  //   try {
  //     const response = await fetchWithRefresh(
  //       `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`,
  //       { method: "GET", credentials: "include" },
  //       router
  //     );

  //     if (!response) {
  //       throw new Error("Session expired, please log in again.");
  //     }
  //     if (!response.ok) {
  //       throw new Error("Failed to fetch visits");
  //     }

  //     const data = await response.json();
  //     const formattedVisits = (data.data ?? data).map((visit: any) => ({
  //       visitId: visit.visitId.toString(),
  //       visitDate: new Date(visit.visitDate).toLocaleDateString(),
  //       dentistName: displayName || visit.dentistName,
  //       createdAt: new Date(visit.createdAt).toLocaleString(),
  //       procedures: visit.procedures || [],
  //       imageCount: visit.imageCount || 0,
  //     }));

  //     dispatch(addPreviousPatientsVisits(formattedVisits));
  //   } catch (err: any) {
  //     console.error("Error fetching visits:", err);
  //   }
  // };

  const handleVisitSelect = (visit: Visit) => {
    setSelectedVisit(visit);
  };

  const handleImageAnalyze = (slotId: string, image: XrayImage) => {
    console.log("Image analyzed:", slotId, image);
  };

  // const handleOpenAnnotator = (slotId: string, image: XrayImage, annotations: Annotation) => {
  //   console.log("in page.tsx", slotId);
  //   setSelectedImage({ slotId, image });
  //   setAnnotations((prev) => ({ ...prev, [slotId]: annotations }));
  //   setAnnotatorOpen(true);
  // };

    const handleOpenAnnotator = (slotId: string, image: XrayImage, annotations: Annotation) => {

    console.log("in page.tsx", slotId);

    setSelectedImage({ slotId, image });

    setAnnotations((prev) => ({ ...prev, [slotId]: annotations }));



    // Store data in sessionStorage for the AI-annotator page (only image info, annotations come from Redux)

    const annotatorData = {

      slotId,

      image

    };

    // Store selected image in Redux instead of sessionStorage
    (dispatch as AppDispatch)(setSelectedAnnotatorImage({ slotId, image }));



    router.push("/ai-annotator");

  };



  const handleOpenAnnotatorFromTreatment = (imageUrl: string, findingId?: string) => {
    const mockImage: XrayImage = {
      id: "treatment-xray",
      imageId: 0, // Added dummy imageId
      url: imageUrl,
      type: "periapical",
      position: "treatment",
      date: new Date().toISOString().split("T")[0],
      analyzed: true,
      findings: [],
    };
    setAnnotations((prev) => ({
      ...prev,
      treatment: prev.treatment || { decay: [], numbering: [] },
    }));
    setSelectedImage({ slotId: "treatment", image: mockImage });
    setAnnotatorOpen(true);
  };

// In patient page
// Replace your handleAnnotatorClose with this:
const handleAnnotatorClose = async () => {
  try {
    console.log("Closing annotator, running analysis..."); // Debug log
    if (runAnalysis && typeof runAnalysis === 'function') {
      await runAnalysis();
    }
    setAnnotatorOpen(false);
  } catch (error) {
    console.error("Error in handleAnnotatorClose:", error);
    setAnnotatorOpen(false);
  }
};
  const registerRunAnalysis = useCallback(
    (fn: () => Promise<void>) => {
      setRunAnalysis(() => fn);
    },
    [] // no deps → identity stays the same
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading patient data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-red-600 mb-2">Error Loading Patient</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-red-600 mb-2">Patient Not Found</div>
          <div className="text-gray-600">
            Patient with ID "{patientId}" does not exist.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background py-3">
      {/* Upload Progress Bar */}
     

      <div className="container max-w-full px-2 md:px-4 mx-auto space-y-4">
        <div className="sticky top-0 z-10 bg-card/80 backdrop-blur-md shadow-sm">
          <input
            id="global-bulk-upload-input"
            type="file"
            accept="image/*"
            multiple
            hidden
           onChange={e => onBulkUpload(e.target.files, router)}
          />
          <PatientHeader 
            patient={patient} 
            compact={true} 
            isAnalyzing={isAnalyzing}
            onBulkUploadClick={() => {
              document.getElementById("global-bulk-upload-input")!.click()
            }}
            onRunAnalysisClick={() => 
              {
                setIsAnalyzing(true);
                setAnalysisProgress(0);
                setAnalysisStatus("Starting analysis...");
                runAnalysis().finally(() => {
                  setIsAnalyzing(false);
                  setAnalysisStatus(null);
                  setAnalysisProgress(0)})}}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
            uploadStatus={uploadStatus}
            
          />
      
         
        </div>

        <Tabs defaultValue="fms" className="space-y-6" value={activeTab} onValueChange={(value) => setActiveTab(value as "fms" | "comparison" | "treatment")}>
          <TabsList className="grid w-full grid-cols-3 bg-card/90 backdrop-blur-md border border-border rounded-xl overflow-hidden">
            <TabsTrigger
              value="fms"
              className="pt-[0.375rem] pb-[1.375rem] min-h-full text-base text-muted-foreground transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              Full Mouth Series
            </TabsTrigger>

            <TabsTrigger
              value="comparison"
              className="pt-[0.375rem] pb-[1.375rem] min-h-full text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground transition-all"
            >
              Comparison Timeline
            </TabsTrigger>
            <TabsTrigger
              value="treatment"
              className="pt-[0.375rem] pb-[1.375rem] min-h-[44px] text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground transition-all"
            >
              Treatment Plan
            </TabsTrigger>
          </TabsList>

          <TabsContent value="fms" className="space-y-g6">
            <FullMouthSeries
              annotatorOpen={annotatorOpen}
              isAnalyzing={isAnalyzing}
              analysisProgress={analysisProgress}
              analysisStatus={analysisStatus}
              onImageAnalyze={handleImageAnalyze}
              onOpenAnnotator={handleOpenAnnotator}
              onVisitSelect={handleVisitSelect}
              selectedVisit={selectedVisit}
              setIsAnalyzing={setIsAnalyzing}
              setAnalysisProgress={setAnalysisProgress}
              setAnalysisStatus={setAnalysisStatus}
              currentid={currentid}
              setcurrentid={setcurrentid}
              onBulkUpload={onBulkUpload}
              isUploading={isUploading}
              uploadProgress={uploadProgress}
              uploadStatus={uploadStatus}
              setUploadProgress={setUploadProgress}
              setIsUploading={setIsUploading}
              setUploadStatus={setUploadStatus}
              handleload={() => {
                if (selectedVisit) {
                  handleLoadImages(selectedVisit);
                }
              }}
              onImagesLoad={(images: any) => {
                if (selectedVisit) {
                  setSelectedVisit({
                    ...selectedVisit,
                    xrays: images,
                    imageCount: images.length
                  });
                }
              }}
              onRegisterRunAnalysis={registerRunAnalysis}
              onBulkUploadClick={() => {
                document.getElementById("global-bulk-upload-input")!.click();
              }}
              onRunAnalysisClick={() => {
                setIsAnalyzing(true);
                setAnalysisProgress(0);
                setAnalysisStatus("Starting analysis...");
                runAnalysis().finally(() => {
                  setIsAnalyzing(false);
                  setAnalysisStatus(null);
                  setAnalysisProgress(0);
                });
              }}
              setslots={(slots: any) => dispatch(setSlots(slots))}
            />

            <div className="mt-4">
              <PreviousVisitsTable
                patientId={patientId!} // Non-null assertion
                onVisitSelect={handleVisitSelect}
                currentid={currentid}
                setcurrentid={setcurrentid}
                selectedVisitId={selectedVisit?.visitId}
                onImagesLoad={(images: any) => {
                  if (selectedVisit) {
                    setSelectedVisit({
                      ...selectedVisit,
                      xrays: images,
                      imageCount: images.length
                    });
                  }
                }}
                
                onBulkUpload={onBulkUpload}
              />
            </div>
          </TabsContent>

          <TabsContent value="comparison">
            <ComparisonTimeline visits={visits} />
          </TabsContent>

          <TabsContent value="treatment">
            <TreatmentPlanView
              patientId={patientId!} // Non-null assertion
              onOpenAnnotator={handleOpenAnnotatorFromTreatment}
            />
          </TabsContent>
        </Tabs>
       
      </div>
    </div>
  );
}
